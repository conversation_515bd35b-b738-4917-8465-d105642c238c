#!/usr/bin/env python3
"""
任务管理器启动脚本

启动和管理任务调度系统
"""

import asyncio
import logging
import signal
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.core.persistent_task_manager import PersistentTaskManager
from backend.app.core.task_manager import TaskManagerConfig
from backend.app.core.task_splitter import SplitterConfig
from backend.app.core.rate_limiter import RateLimitConfig
from backend.app.core.retry_manager import RetryConfig
from config.settings import get_settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/task_manager.log')
    ]
)

logger = logging.getLogger(__name__)


class TaskManagerService:
    """任务管理器服务"""
    
    def __init__(self):
        self.task_manager: PersistentTaskManager = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()
    
    async def start(self):
        """启动服务"""
        try:
            logger.info("Starting Task Manager Service...")
            
            # 获取配置
            settings = get_settings()
            
            # 配置任务分片器
            splitter_config = SplitterConfig(
                max_batch_size=100,
                max_concurrent_batches=2,
                min_batch_size=10,
                adaptive_sizing=True,
                load_balance_enabled=True
            )
            
            # 配置限流器
            rate_limiter_config = RateLimitConfig(
                max_concurrent_requests=2,  # 外部API限制
                requests_per_minute=60,
                requests_per_hour=1000,
                failure_threshold=5,
                failure_rate_threshold=0.5,
                recovery_timeout=60,
                redis_url=settings.REDIS_URL
            )
            
            # 配置重试管理器
            retry_config = RetryConfig(
                max_retries=3,
                base_delay=60.0,
                max_delay=3600.0,
                backoff_multiplier=2.0,
                jitter=True
            )

            # 配置任务管理器
            manager_config = TaskManagerConfig(
                max_concurrent_batches=2,
                batch_check_interval=5.0,
                task_timeout=1800,  # 30分钟
                retry_failed_interval=300,  # 5分钟
                cleanup_interval=3600,  # 1小时
                max_queue_size=10000,
                splitter_config=splitter_config,
                rate_limiter_config=rate_limiter_config,
                retry_config=retry_config
            )
            
            # 创建持久化任务管理器
            redis_url = settings.REDIS_URL if hasattr(settings, 'REDIS_URL') else "redis://localhost:6379/0"
            self.task_manager = PersistentTaskManager(manager_config, redis_url)
            
            # 启动任务管理器
            await self.task_manager.start()
            
            self.is_running = True
            logger.info("Task Manager Service started successfully")
            
            # 注册信号处理器
            self._setup_signal_handlers()
            
            # 启动状态监控
            asyncio.create_task(self._status_monitor())
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"Failed to start Task Manager Service: {e}")
            raise
    
    async def stop(self):
        """停止服务"""
        try:
            logger.info("Stopping Task Manager Service...")
            
            if self.task_manager:
                await self.task_manager.stop()
            
            self.is_running = False
            self.shutdown_event.set()
            
            logger.info("Task Manager Service stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Task Manager Service: {e}")
            raise
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def _status_monitor(self):
        """状态监控"""
        while self.is_running:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                
                if self.task_manager:
                    status = self.task_manager.get_status()
                    
                    logger.info(
                        f"Task Manager Status: "
                        f"Pending: {status['pending_batches']}, "
                        f"Running: {status['running_batches']}, "
                        f"Completed: {status['completed_batches']}, "
                        f"Failed: {status['failed_batches']}"
                    )
                    
                    # 检查是否有异常情况
                    if status['status'] == 'error':
                        logger.error("Task Manager is in error state!")
                    
                    if status['pending_batches'] > 1000:
                        logger.warning(f"High number of pending batches: {status['pending_batches']}")
                    
                    if status['failed_batches'] > 100:
                        logger.warning(f"High number of failed batches: {status['failed_batches']}")
            
            except Exception as e:
                logger.error(f"Error in status monitor: {e}")
    
    async def submit_test_task(self):
        """提交测试任务"""
        if not self.task_manager:
            logger.error("Task manager not initialized")
            return
        
        try:
            # 生成测试URL
            test_urls = [
                f"https://mercadolibre.com.ar/product/test_{i}" 
                for i in range(150)
            ]
            
            submission_id = await self.task_manager.submit_task(
                task_id=999,
                urls=test_urls,
                platform="mercadolibre",
                priority=TaskPriority.NORMAL
            )
            
            logger.info(f"Test task submitted: {submission_id}")
            
        except Exception as e:
            logger.error(f"Failed to submit test task: {e}")


async def main():
    """主函数"""
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    service = TaskManagerService()
    
    try:
        # 检查命令行参数
        if len(sys.argv) > 1:
            command = sys.argv[1]
            
            if command == "test":
                # 运行测试模式
                logger.info("Running in test mode...")
                await service.start()
                
                # 等待服务启动
                await asyncio.sleep(5)
                
                # 提交测试任务
                await service.submit_test_task()
                
                # 运行30分钟后自动停止
                await asyncio.sleep(1800)
                await service.stop()
                
            elif command == "status":
                # 显示状态信息
                logger.info("Checking task manager status...")
                # 这里可以添加状态检查逻辑
                
            else:
                logger.error(f"Unknown command: {command}")
                sys.exit(1)
        else:
            # 正常运行模式
            await service.start()
    
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Service error: {e}")
        sys.exit(1)
    finally:
        if service.is_running:
            await service.stop()


if __name__ == "__main__":
    # 导入必要的模块
    from app.core.task_splitter import TaskPriority
    
    asyncio.run(main())

"""
Celery监控API路由
提供Celery任务、Worker、队列的实时监控功能
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from app.celery_app import celery_app
from app.core.redis_client import get_redis_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/celery", tags=["Celery监控"])


# 响应模型
class CeleryTaskInfo(BaseModel):
    id: str
    name: str
    state: str
    args: List[Any]
    kwargs: Dict[str, Any]
    result: Optional[Any] = None
    traceback: Optional[str] = None
    timestamp: str
    runtime: Optional[float] = None
    worker: Optional[str] = None
    queue: Optional[str] = None
    retries: Optional[int] = None
    eta: Optional[str] = None


class CeleryWorkerInfo(BaseModel):
    name: str
    status: str
    active_tasks: int
    processed_tasks: int
    load_avg: List[float]
    pool_size: int
    pool_writes: int
    clock: int


class QueueInfo(BaseModel):
    name: str
    length: int
    consumers: int
    messages: int
    memory: int


class CeleryStats(BaseModel):
    total_tasks: int
    active_tasks: int
    pending_tasks: int
    success_tasks: int
    failed_tasks: int
    workers_online: int
    total_queues: int


@router.get("/stats", response_model=CeleryStats)
async def get_celery_stats():
    """获取Celery统计信息"""
    try:
        inspect = celery_app.control.inspect()
        
        # 获取基本统计
        stats = inspect.stats()
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        
        # 计算统计数据
        workers_online = len(stats or {})
        total_active = sum(len(tasks) for tasks in (active_tasks or {}).values())
        total_scheduled = sum(len(tasks) for tasks in (scheduled_tasks or {}).values())
        
        # 从Redis获取历史统计
        redis_client = await get_redis_client()
        
        # 获取任务计数
        total_tasks = await redis_client.get("celery:stats:total_tasks") or 0
        success_tasks = await redis_client.get("celery:stats:success_tasks") or 0
        failed_tasks = await redis_client.get("celery:stats:failed_tasks") or 0
        
        return CeleryStats(
            total_tasks=int(total_tasks),
            active_tasks=total_active,
            pending_tasks=total_scheduled,
            success_tasks=int(success_tasks),
            failed_tasks=int(failed_tasks),
            workers_online=workers_online,
            total_queues=1  # 默认队列数量
        )
        
    except Exception as e:
        logger.error(f"Failed to get Celery stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/active", response_model=List[CeleryTaskInfo])
async def get_active_tasks():
    """获取活跃任务列表"""
    try:
        inspect = celery_app.control.inspect()
        
        # 获取活跃任务
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        
        tasks = []
        
        # 处理活跃任务
        if active_tasks:
            for worker, worker_tasks in active_tasks.items():
                for task in worker_tasks:
                    # 安全地获取任务信息
                    task_id = task.get('id', task.get('uuid', ''))
                    task_name = task.get('name', task.get('type', ''))
                    task_args = task.get('args', [])
                    task_kwargs = task.get('kwargs', {})

                    # 处理时间戳
                    timestamp = task.get('time_start')
                    if not timestamp:
                        timestamp = task.get('timestamp')

                    # 确保时间戳是字符串格式
                    if isinstance(timestamp, (int, float)):
                        timestamp = datetime.fromtimestamp(timestamp).isoformat()
                    elif not timestamp:
                        timestamp = datetime.now().isoformat()

                    # 处理队列信息
                    delivery_info = task.get('delivery_info', {})
                    queue = 'default'
                    if isinstance(delivery_info, dict):
                        queue = delivery_info.get('routing_key', 'default')

                    tasks.append(CeleryTaskInfo(
                        id=task_id,
                        name=task_name,
                        state='STARTED',
                        args=task_args,
                        kwargs=task_kwargs,
                        timestamp=timestamp,
                        worker=worker,
                        queue=queue
                    ))
        
        # 处理计划任务
        if scheduled_tasks:
            for worker, worker_tasks in scheduled_tasks.items():
                for task in worker_tasks:
                    # 安全地获取任务信息
                    task_id = task.get('id', task.get('uuid', ''))
                    task_name = task.get('name', task.get('type', ''))
                    task_args = task.get('args', [])
                    task_kwargs = task.get('kwargs', {})

                    # 处理ETA和时间戳
                    eta = task.get('eta')
                    timestamp = eta if eta else datetime.now().isoformat()

                    # 处理队列信息
                    delivery_info = task.get('delivery_info', {})
                    queue = 'default'
                    if isinstance(delivery_info, dict):
                        queue = delivery_info.get('routing_key', 'default')

                    tasks.append(CeleryTaskInfo(
                        id=task_id,
                        name=task_name,
                        state='PENDING',
                        args=task_args,
                        kwargs=task_kwargs,
                        timestamp=timestamp,
                        worker=worker,
                        eta=eta,
                        queue=queue
                    ))
        
        return tasks
        
    except Exception as e:
        logger.error(f"Failed to get active tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/history", response_model=List[CeleryTaskInfo])
async def get_task_history(
    limit: int = Query(100, description="返回任务数量限制"),
    state: Optional[str] = Query(None, description="任务状态筛选"),
    worker: Optional[str] = Query(None, description="Worker筛选")
):
    """获取任务历史记录"""
    try:
        redis_client = await get_redis_client()
        
        # 从Redis获取任务历史
        task_keys = await redis_client.keys("celery:task:*")
        tasks = []
        
        for key in task_keys[-limit:]:  # 限制数量
            task_data = await redis_client.hgetall(key)
            if task_data:
                # 应用筛选条件
                if state and task_data.get('state') != state:
                    continue
                if worker and task_data.get('worker') != worker:
                    continue
                
                tasks.append(CeleryTaskInfo(
                    id=task_data.get('id', ''),
                    name=task_data.get('name', ''),
                    state=task_data.get('state', 'UNKNOWN'),
                    args=eval(task_data.get('args', '[]')),
                    kwargs=eval(task_data.get('kwargs', '{}')),
                    result=task_data.get('result'),
                    traceback=task_data.get('traceback'),
                    timestamp=task_data.get('timestamp', ''),
                    runtime=float(task_data.get('runtime', 0)) if task_data.get('runtime') else None,
                    worker=task_data.get('worker'),
                    queue=task_data.get('queue'),
                    retries=int(task_data.get('retries', 0)) if task_data.get('retries') else None
                ))
        
        # 按时间戳排序
        tasks.sort(key=lambda x: x.timestamp, reverse=True)
        
        return tasks
        
    except Exception as e:
        logger.error(f"Failed to get task history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workers", response_model=List[CeleryWorkerInfo])
async def get_workers():
    """获取Worker状态"""
    try:
        inspect = celery_app.control.inspect()
        
        # 获取Worker统计信息
        stats = inspect.stats()
        active_tasks = inspect.active()
        
        workers = []
        
        if stats:
            for worker_name, worker_stats in stats.items():
                # 获取该Worker的活跃任务数
                worker_active_tasks = len((active_tasks or {}).get(worker_name, []))
                
                # 安全地获取统计数据
                total_stats = worker_stats.get('total', {})
                rusage_stats = worker_stats.get('rusage', {})
                pool_stats = worker_stats.get('pool', {})

                # 处理processed_tasks
                processed_tasks = 0
                if isinstance(total_stats, dict):
                    for key, value in total_stats.items():
                        if 'crawl' in key and isinstance(value, int):
                            processed_tasks += value

                # 处理load_avg
                load_avg = [0.0, 0.0, 0.0]
                if isinstance(rusage_stats, dict) and 'load_avg' in rusage_stats:
                    load_data = rusage_stats['load_avg']
                    if isinstance(load_data, list) and len(load_data) >= 3:
                        load_avg = [float(x) for x in load_data[:3]]

                # 处理pool数据
                pool_size = 0
                pool_writes = 0
                if isinstance(pool_stats, dict):
                    pool_size = int(pool_stats.get('max-concurrency', 0))
                    pool_writes_data = pool_stats.get('writes', 0)
                    if isinstance(pool_writes_data, dict):
                        pool_writes = int(pool_writes_data.get('total', 0))
                    else:
                        pool_writes = int(pool_writes_data)

                workers.append(CeleryWorkerInfo(
                    name=worker_name,
                    status='online',
                    active_tasks=worker_active_tasks,
                    processed_tasks=processed_tasks,
                    load_avg=load_avg,
                    pool_size=pool_size,
                    pool_writes=pool_writes,
                    clock=int(worker_stats.get('clock', 0))
                ))
        
        return workers
        
    except Exception as e:
        logger.error(f"Failed to get workers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/queues", response_model=List[QueueInfo])
async def get_queues():
    """获取队列信息"""
    try:
        redis_client = await get_redis_client()
        
        # 获取队列信息
        queues = []
        
        # 检查默认队列
        queue_length = await redis_client.llen("celery")
        
        queues.append(QueueInfo(
            name="celery",
            length=queue_length,
            consumers=1,  # 假设有一个消费者
            messages=queue_length,
            memory=queue_length * 1024  # 估算内存使用
        ))
        
        # 检查其他可能的队列
        for queue_name in ["crawl", "monitor", "high", "low"]:
            queue_length = await redis_client.llen(queue_name)
            if queue_length > 0:
                queues.append(QueueInfo(
                    name=queue_name,
                    length=queue_length,
                    consumers=1,
                    messages=queue_length,
                    memory=queue_length * 1024
                ))
        
        return queues
        
    except Exception as e:
        logger.error(f"Failed to get queues: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/revoke")
async def revoke_task(task_id: str):
    """撤销任务"""
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        # 记录撤销操作
        redis_client = await get_redis_client()
        await redis_client.hset(
            f"celery:task:{task_id}",
            mapping={
                "state": "REVOKED",
                "revoked_at": datetime.now().isoformat(),
                "revoked_by": "admin"
            }
        )
        
        return {"success": True, "message": f"Task {task_id} revoked successfully"}
        
    except Exception as e:
        logger.error(f"Failed to revoke task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/detail", response_model=CeleryTaskInfo)
async def get_task_detail(task_id: str):
    """获取任务详细信息"""
    try:
        redis_client = await get_redis_client()
        
        # 从Redis获取任务详情
        task_data = await redis_client.hgetall(f"celery:task:{task_id}")
        
        if not task_data:
            # 尝试从活跃任务中查找
            inspect = celery_app.control.inspect()
            active_tasks = inspect.active()
            
            if active_tasks:
                for worker, worker_tasks in active_tasks.items():
                    for task in worker_tasks:
                        if task['id'] == task_id:
                            return CeleryTaskInfo(
                                id=task['id'],
                                name=task['name'],
                                state='STARTED',
                                args=task.get('args', []),
                                kwargs=task.get('kwargs', {}),
                                timestamp=task.get('time_start', datetime.now().isoformat()),
                                worker=worker,
                                queue=task.get('delivery_info', {}).get('routing_key', 'default')
                            )
            
            raise HTTPException(status_code=404, detail="Task not found")
        
        return CeleryTaskInfo(
            id=task_data.get('id', task_id),
            name=task_data.get('name', ''),
            state=task_data.get('state', 'UNKNOWN'),
            args=eval(task_data.get('args', '[]')),
            kwargs=eval(task_data.get('kwargs', '{}')),
            result=task_data.get('result'),
            traceback=task_data.get('traceback'),
            timestamp=task_data.get('timestamp', ''),
            runtime=float(task_data.get('runtime', 0)) if task_data.get('runtime') else None,
            worker=task_data.get('worker'),
            queue=task_data.get('queue'),
            retries=int(task_data.get('retries', 0)) if task_data.get('retries') else None
        )
        
    except Exception as e:
        logger.error(f"Failed to get task detail {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def celery_health_check():
    """Celery健康检查"""
    try:
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if not stats:
            return {
                "status": "unhealthy",
                "message": "No workers available",
                "workers": 0,
                "timestamp": datetime.now().isoformat()
            }
        
        return {
            "status": "healthy",
            "message": "Celery is running normally",
            "workers": len(stats),
            "worker_names": list(stats.keys()),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        return {
            "status": "unhealthy",
            "message": str(e),
            "workers": 0,
            "timestamp": datetime.now().isoformat()
        }

services:
  timescaledb:
    image: timescale/timescaledb:latest-pg14
    container_name: monit-timescaledb
    environment:
      POSTGRES_DB: monit_crawler_dev
      POSTGRES_USER: crawler_user
      POSTGRES_PASSWORD: crawler_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - monit-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: monit-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - monit-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: monit-backend
    depends_on:
      - timescaledb
      - redis
    environment:
      - DATABASE_URL=***********************************************************/monit_crawler_dev
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
      - DOCKER_ENV=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./data:/app/data
    networks:
      - monit-network
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: monit-frontend
    depends_on:
      - backend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - monit-network
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000/ws
      - CHOKIDAR_USEPOLLING=true
      - CHOKIDAR_INTERVAL=1000
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=true
      - HOST=0.0.0.0

  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: monit-celery-worker
    depends_on:
      - timescaledb
      - redis
    environment:
      - DATABASE_URL=***********************************************************/monit_crawler_dev
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./data:/app/data
    networks:
      - monit-network
    restart: unless-stopped
    command: celery -A app.celery_app worker --loglevel=info

  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: monit-celery-beat
    depends_on:
      - timescaledb
      - redis
    environment:
      - DATABASE_URL=***********************************************************/monit_crawler_dev
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./data:/app/data
    networks:
      - monit-network
    restart: unless-stopped
    command: celery -A app.celery_app beat --loglevel=info

volumes:
  timescale_data:
  redis_data:

networks:
  monit-network:
    driver: bridge 
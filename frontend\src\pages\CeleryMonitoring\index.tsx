import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Tag,
  Statistic,
  Progress,
  Typography,
  Space,
  Button,
  Tabs,
  Alert,
  Tooltip,
  Modal,
  Input,
  Select,
  DatePicker,
  message
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  BarsOutlined,
  WarningOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import './index.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 类型定义
interface CeleryTask {
  id: string;
  name: string;
  state: 'PENDING' | 'STARTED' | 'SUCCESS' | 'FAILURE' | 'RETRY' | 'REVOKED';
  args: any[];
  kwargs: any;
  result?: any;
  traceback?: string;
  timestamp: string;
  runtime?: number;
  worker?: string;
  queue?: string;
  retries?: number;
  eta?: string;
}

interface CeleryWorker {
  name: string;
  status: 'online' | 'offline';
  active_tasks: number;
  processed_tasks: number;
  load_avg: number[];
  pool_size: number;
  pool_writes: number;
  rusage: any;
  clock: number;
}

interface QueueInfo {
  name: string;
  length: number;
  consumers: number;
  messages: number;
  memory: number;
}

interface CeleryStats {
  total_tasks: number;
  active_tasks: number;
  pending_tasks: number;
  success_tasks: number;
  failed_tasks: number;
  workers_online: number;
  total_queues: number;
}

const CeleryMonitoring: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);

  // 数据状态
  const [stats, setStats] = useState<CeleryStats>({
    total_tasks: 0,
    active_tasks: 0,
    pending_tasks: 0,
    success_tasks: 0,
    failed_tasks: 0,
    workers_online: 0,
    total_queues: 0
  });
  const [tasks, setTasks] = useState<CeleryTask[]>([]);
  const [workers, setWorkers] = useState<CeleryWorker[]>([]);
  const [queues, setQueues] = useState<QueueInfo[]>([]);

  // 筛选状态
  const [taskFilter, setTaskFilter] = useState<string>('all');
  const [workerFilter, setWorkerFilter] = useState<string>('all');

  // 获取Celery统计信息
  const fetchCeleryStats = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/celery/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch Celery stats:', error);
    }
  }, []);

  // 获取活跃任务
  const fetchActiveTasks = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/celery/tasks/active');
      if (response.ok) {
        const data = await response.json();
        setTasks(data);
      }
    } catch (error) {
      console.error('Failed to fetch active tasks:', error);
    }
  }, []);

  // 获取Worker状态
  const fetchWorkers = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/celery/workers');
      if (response.ok) {
        const data = await response.json();
        setWorkers(data);
      }
    } catch (error) {
      console.error('Failed to fetch workers:', error);
    }
  }, []);

  // 获取队列信息
  const fetchQueues = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/celery/queues');
      if (response.ok) {
        const data = await response.json();
        setQueues(data);
      }
    } catch (error) {
      console.error('Failed to fetch queues:', error);
    }
  }, []);

  // 刷新所有数据
  const refreshData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchCeleryStats(),
        fetchActiveTasks(),
        fetchWorkers(),
        fetchQueues()
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchCeleryStats, fetchActiveTasks, fetchWorkers, fetchQueues]);

  // 自动刷新
  useEffect(() => {
    refreshData();
    
    if (autoRefresh) {
      const interval = setInterval(refreshData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshData, autoRefresh, refreshInterval]);

  // 任务状态配置
  const taskStateConfig = {
    PENDING: { color: 'default', text: '等待中', icon: <ClockCircleOutlined /> },
    STARTED: { color: 'processing', text: '执行中', icon: <PlayCircleOutlined /> },
    SUCCESS: { color: 'success', text: '成功', icon: <CheckCircleOutlined /> },
    FAILURE: { color: 'error', text: '失败', icon: <ExclamationCircleOutlined /> },
    RETRY: { color: 'warning', text: '重试中', icon: <ReloadOutlined /> },
    REVOKED: { color: 'default', text: '已撤销', icon: <StopOutlined /> }
  };

  // 任务表格列定义
  const taskColumns: ColumnsType<CeleryTask> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      render: (id: string) => (
        <Text code style={{ fontSize: '12px' }}>{id.substring(0, 8)}...</Text>
      )
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name: string) => (
        <Text>{name.split('.').pop()}</Text>
      )
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (state: string) => {
        const config = taskStateConfig[state as keyof typeof taskStateConfig] || taskStateConfig.PENDING;
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: 'Worker',
      dataIndex: 'worker',
      key: 'worker',
      width: 150,
      render: (worker: string) => worker ? <Text code>{worker}</Text> : '-'
    },
    {
      title: '队列',
      dataIndex: 'queue',
      key: 'queue',
      width: 100,
      render: (queue: string) => queue ? <Tag>{queue}</Tag> : '-'
    },
    {
      title: '运行时间',
      dataIndex: 'runtime',
      key: 'runtime',
      width: 100,
      render: (runtime: number) => runtime ? `${runtime.toFixed(2)}s` : '-'
    },
    {
      title: '开始时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 150,
      render: (timestamp: string) => new Date(timestamp).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button size="small" icon={<EyeOutlined />} />
          </Tooltip>
          {record.state === 'STARTED' && (
            <Tooltip title="撤销任务">
              <Button size="small" danger icon={<DeleteOutlined />} />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // Worker表格列定义
  const workerColumns: ColumnsType<CeleryWorker> = [
    {
      title: 'Worker名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => <Text code>{name}</Text>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '离线'}
        </Tag>
      )
    },
    {
      title: '活跃任务',
      dataIndex: 'active_tasks',
      key: 'active_tasks',
      render: (count: number) => <Text strong>{count}</Text>
    },
    {
      title: '已处理任务',
      dataIndex: 'processed_tasks',
      key: 'processed_tasks'
    },
    {
      title: '进程池大小',
      dataIndex: 'pool_size',
      key: 'pool_size'
    },
    {
      title: '负载',
      dataIndex: 'load_avg',
      key: 'load_avg',
      render: (load: number[]) => load ? load.map(l => l.toFixed(2)).join(', ') : '-'
    }
  ];

  return (
    <div className="celery-monitoring">
      <div className="celery-monitoring-header">
        <Title level={2}>Celery任务监控</Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshData}
            loading={loading}
          >
            刷新
          </Button>
          <Select
            value={autoRefresh}
            onChange={setAutoRefresh}
            style={{ width: 120 }}
          >
            <Option value={true}>自动刷新</Option>
            <Option value={false}>手动刷新</Option>
          </Select>
          {autoRefresh && (
            <Select
              value={refreshInterval}
              onChange={setRefreshInterval}
              style={{ width: 100 }}
            >
              <Option value={3000}>3秒</Option>
              <Option value={5000}>5秒</Option>
              <Option value={10000}>10秒</Option>
              <Option value={30000}>30秒</Option>
            </Select>
          )}
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="总任务数"
              value={stats.total_tasks}
              prefix={<BarsOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="活跃任务"
              value={stats.active_tasks}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="等待任务"
              value={stats.pending_tasks}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="成功任务"
              value={stats.success_tasks}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="失败任务"
              value={stats.failed_tasks}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="在线Worker"
              value={stats.workers_online}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="overview">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="队列状态" size="small">
                  <Table
                    dataSource={queues}
                    columns={[
                      { title: '队列名称', dataIndex: 'name', key: 'name' },
                      { title: '消息数量', dataIndex: 'length', key: 'length' },
                      { title: '消费者', dataIndex: 'consumers', key: 'consumers' }
                    ]}
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="Worker状态" size="small">
                  <Table
                    dataSource={workers}
                    columns={workerColumns.slice(0, 4)}
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab="活跃任务" key="tasks">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  value={taskFilter}
                  onChange={setTaskFilter}
                  style={{ width: 120 }}
                >
                  <Option value="all">全部状态</Option>
                  <Option value="STARTED">执行中</Option>
                  <Option value="PENDING">等待中</Option>
                  <Option value="FAILURE">失败</Option>
                </Select>
              </Space>
            </div>
            <Table
              dataSource={tasks.filter(task => 
                taskFilter === 'all' || task.state === taskFilter
              )}
              columns={taskColumns}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 20 }}
            />
          </TabPane>
          
          <TabPane tab="Worker管理" key="workers">
            <Table
              dataSource={workers}
              columns={workerColumns}
              rowKey="name"
              loading={loading}
            />
          </TabPane>
          
          <TabPane tab="队列监控" key="queues">
            <Table
              dataSource={queues}
              columns={[
                { title: '队列名称', dataIndex: 'name', key: 'name' },
                { title: '消息数量', dataIndex: 'length', key: 'length' },
                { title: '消费者数量', dataIndex: 'consumers', key: 'consumers' },
                { title: '内存使用', dataIndex: 'memory', key: 'memory', render: (mem: number) => `${(mem / 1024 / 1024).toFixed(2)} MB` }
              ]}
              rowKey="name"
              loading={loading}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default CeleryMonitoring;

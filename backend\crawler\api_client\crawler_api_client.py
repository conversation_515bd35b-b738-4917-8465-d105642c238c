"""
爬虫API客户端

实现对外部爬虫服务的HTTP API调用。
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import json

import httpx
from httpx import AsyncClient, Response

from .api_config import APIConfig
from .api_exceptions import (
    APIException, 
    APITimeoutException, 
    APIAuthException,
    APIRateLimitException,
    APIServerException,
    APIValidationException
)


class CrawlerAPIClient:
    """爬虫API客户端"""
    
    def __init__(self, config: Optional[APIConfig] = None):
        """初始化API客户端
        
        Args:
            config: API配置，如果为None则使用默认配置
        """
        self.config = config or APIConfig.from_env()
        self.config.validate()
        
        self.logger = logging.getLogger(__name__)
        self.client: Optional[AsyncClient] = None
        
        # 限流控制
        self._request_times: List[float] = []
        self._rate_limit_lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            "requests_sent": 0,
            "requests_successful": 0,
            "requests_failed": 0,
            "total_response_time": 0.0,
            "last_request_time": None
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self) -> None:
        """启动客户端"""
        if self.client is None:
            self.client = AsyncClient(
                timeout=httpx.Timeout(self.config.timeout),
                verify=self.config.verify_ssl,
                headers=self.config.headers
            )
            self.logger.info("API client started")
    
    async def close(self) -> None:
        """关闭客户端"""
        if self.client:
            await self.client.aclose()
            self.client = None
            self.logger.info("API client closed")
    
    async def _check_rate_limit(self) -> None:
        """检查限流"""
        async with self._rate_limit_lock:
            now = time.time()
            
            # 清理过期的请求时间
            cutoff_time = now - self.config.rate_limit_period
            self._request_times = [t for t in self._request_times if t > cutoff_time]
            
            # 检查是否超过限流
            if len(self._request_times) >= self.config.rate_limit_requests:
                sleep_time = self._request_times[0] + self.config.rate_limit_period - now
                if sleep_time > 0:
                    self.logger.warning(f"Rate limit reached, sleeping for {sleep_time:.2f}s")
                    await asyncio.sleep(sleep_time)
            
            # 记录当前请求时间
            self._request_times.append(now)
    
    async def _make_request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Response:
        """发送HTTP请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            Response: HTTP响应
        """
        if not self.client:
            await self.start()
        
        # 限流检查
        await self._check_rate_limit()
        
        start_time = time.time()
        self.stats["requests_sent"] += 1
        
        try:
            response = await self.client.request(method, url, **kwargs)
            
            # 更新统计信息
            response_time = time.time() - start_time
            self.stats["total_response_time"] += response_time
            self.stats["last_request_time"] = datetime.now()
            
            # 检查响应状态
            if response.status_code == 401:
                self.stats["requests_failed"] += 1
                raise APIAuthException("Authentication failed")
            elif response.status_code == 429:
                self.stats["requests_failed"] += 1
                retry_after = response.headers.get("Retry-After")
                raise APIRateLimitException(
                    "Rate limit exceeded",
                    retry_after=int(retry_after) if retry_after else None
                )
            elif response.status_code >= 500:
                self.stats["requests_failed"] += 1
                raise APIServerException(
                    f"Server error: {response.text}",
                    status_code=response.status_code
                )
            elif response.status_code >= 400:
                self.stats["requests_failed"] += 1
                raise APIValidationException(
                    f"Client error: {response.text}",
                    validation_errors=response.json() if response.headers.get("content-type", "").startswith("application/json") else None
                )
            
            self.stats["requests_successful"] += 1
            return response
            
        except httpx.TimeoutException as e:
            self.stats["requests_failed"] += 1
            raise APITimeoutException(f"Request timeout: {e}", timeout=self.config.timeout)
        except httpx.RequestError as e:
            self.stats["requests_failed"] += 1
            raise APIException(f"Request error: {e}")
    
    async def _retry_request(
        self,
        method: str,
        url: str,
        max_retries: Optional[int] = None,
        **kwargs
    ) -> Response:
        """带重试的请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            max_retries: 最大重试次数
            **kwargs: 其他请求参数
            
        Returns:
            Response: HTTP响应
        """
        max_retries = max_retries or self.config.max_retries
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return await self._make_request(method, url, **kwargs)
            except (APITimeoutException, APIServerException, APIException) as e:
                last_exception = e
                if attempt < max_retries:
                    delay = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"Request failed (attempt {attempt + 1}/{max_retries + 1}), retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"Request failed after {max_retries + 1} attempts: {e}")
            except APIRateLimitException as e:
                if e.retry_after and attempt < max_retries:
                    self.logger.warning(f"Rate limited, waiting {e.retry_after}s before retry")
                    await asyncio.sleep(e.retry_after)
                else:
                    raise
            except APIAuthException:
                # 认证错误不重试
                raise
        
        raise last_exception

    async def crawl_url(
        self,
        url: str,
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """爬取单个URL

        Args:
            url: 目标URL
            platform: 平台类型
            options: 爬取选项

        Returns:
            Dict[str, Any]: 爬取结果
        """
        endpoint_url = self.config.get_crawl_url()

        payload = {
            "url": url,
            "platform": platform,
            "options": options or {}
        }

        response = await self._retry_request(
            "POST",
            endpoint_url,
            json=payload
        )

        return response.json()

    async def crawl_batch(
        self,
        urls: List[str],
        platform: str = "mercadolibre",
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """批量爬取URL

        Args:
            urls: URL列表
            platform: 平台类型
            options: 爬取选项

        Returns:
            Dict[str, Any]: 批量爬取任务信息
        """
        endpoint_url = self.config.get_batch_crawl_url()

        payload = {
            "urls": urls,
            "platform": platform,
            "options": options or {}
        }

        response = await self._retry_request(
            "POST",
            endpoint_url,
            json=payload
        )

        return response.json()

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """
        endpoint_url = self.config.get_status_url(task_id)

        response = await self._retry_request("GET", endpoint_url)
        return response.json()

    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 任务结果
        """
        endpoint_url = self.config.get_result_url(task_id)

        response = await self._retry_request("GET", endpoint_url)
        return response.json()

    async def wait_for_task_completion(
        self,
        task_id: str,
        timeout: Optional[float] = None,
        poll_interval: float = 2.0
    ) -> Dict[str, Any]:
        """等待任务完成

        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            poll_interval: 轮询间隔（秒）

        Returns:
            Dict[str, Any]: 任务结果
        """
        start_time = time.time()
        timeout = timeout or self.config.timeout * 10  # 默认为请求超时的10倍

        while True:
            status_info = await self.get_task_status(task_id)
            status = status_info.get("status")

            if status in ["completed", "success"]:
                return await self.get_task_result(task_id)
            elif status in ["failed", "error"]:
                error_msg = status_info.get("error", "Task failed")
                raise APIException(f"Task {task_id} failed: {error_msg}")
            elif status == "cancelled":
                raise APIException(f"Task {task_id} was cancelled")

            # 检查超时
            if time.time() - start_time > timeout:
                raise APITimeoutException(f"Task {task_id} timeout after {timeout}s")

            await asyncio.sleep(poll_interval)

    def get_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()

        # 计算平均响应时间
        if stats["requests_successful"] > 0:
            stats["avg_response_time"] = stats["total_response_time"] / stats["requests_successful"]
        else:
            stats["avg_response_time"] = 0.0

        # 计算成功率
        total_requests = stats["requests_successful"] + stats["requests_failed"]
        if total_requests > 0:
            stats["success_rate"] = stats["requests_successful"] / total_requests
        else:
            stats["success_rate"] = 0.0

        return stats

    async def health_check(self) -> bool:
        """健康检查

        Returns:
            bool: 服务是否健康
        """
        try:
            health_url = self.config.get_endpoint_url("health")
            response = await self._make_request("GET", health_url)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False

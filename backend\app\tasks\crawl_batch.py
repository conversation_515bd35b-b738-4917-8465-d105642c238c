"""
批量爬取任务

处理分片后的URL批次，调用外部爬虫API进行数据采集
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from celery import Task
from celery.exceptions import Retry

from app.celery_app import celery_app
from app.core.rate_limiter import RateLimiter, RateLimitConfig
from crawler.api_client import CrawlerAPIClient, APIConfig
from app.database import get_async_session
from app.models.task import CrawlTask
from app.models.snapshot import ProductSnapshot
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

logger = logging.getLogger(__name__)


class CrawlBatchTask(Task):
    """批量爬取任务基类"""
    
    def __init__(self):
        self.rate_limiter: Optional[RateLimiter] = None
        self.api_client: Optional[CrawlerAPIClient] = None
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """重试时的回调"""
        logger.warning(f"Task {task_id} retrying due to: {exc}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """失败时的回调"""
        logger.error(f"Task {task_id} failed: {exc}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """成功时的回调"""
        logger.info(f"Task {task_id} completed successfully")


@celery_app.task(
    bind=True,
    base=CrawlBatchTask,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True
)
def crawl_batch_task(
    self,
    batch_id: str,
    urls: List[str],
    platform: str = "mercadolibre",
    task_id: Optional[int] = None,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """批量爬取任务
    
    Args:
        batch_id: 批次ID
        urls: URL列表
        platform: 平台类型
        task_id: 数据库任务ID
        options: 爬取选项
        
    Returns:
        Dict[str, Any]: 爬取结果
    """
    return asyncio.run(_crawl_batch_async(
        batch_id, urls, platform, task_id, options
    ))


async def _crawl_batch_async(
    batch_id: str,
    urls: List[str],
    platform: str,
    task_id: Optional[int],
    options: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步批量爬取实现"""
    
    start_time = datetime.now()
    results = {
        "batch_id": batch_id,
        "total_urls": len(urls),
        "successful_urls": 0,
        "failed_urls": 0,
        "results": [],
        "errors": [],
        "start_time": start_time.isoformat(),
        "end_time": None,
        "duration": 0.0
    }
    
    # 初始化限流器和API客户端
    rate_limiter = RateLimiter(RateLimitConfig())

    # 检查是否有指定的爬虫后端
    crawler_backend = options.get("crawler_backend")
    if crawler_backend:
        # 使用指定的后端配置
        api_config = APIConfig(
            base_url=crawler_backend.get("base_url", "http://localhost:11234"),
            timeout=crawler_backend.get("timeout", 30.0),
            max_retries=crawler_backend.get("max_retries", 3),
            api_key=crawler_backend.get("api_key"),
            auth_type=crawler_backend.get("auth_type", "none")
        )
        logger.info(f"Using crawler backend: {crawler_backend.get('name')} ({crawler_backend.get('base_url')})")
    else:
        # 使用默认配置
        api_config = APIConfig.from_env()
        logger.info("Using default crawler backend configuration")

    api_client = CrawlerAPIClient(api_config)
    
    try:
        await rate_limiter.start()
        await api_client.start()
        
        # 更新任务状态为运行中
        if task_id:
            await _update_task_status(task_id, "running", 0)
        
        logger.info(f"Starting batch {batch_id} with {len(urls)} URLs")
        
        # 处理URL批次
        for i, url in enumerate(urls):
            try:
                # 获取限流许可
                request_id = f"{batch_id}_{i}"
                if not await rate_limiter.acquire(request_id):
                    # 限流拒绝，等待后重试
                    await asyncio.sleep(30)
                    if not await rate_limiter.acquire(request_id):
                        raise Exception("Rate limit exceeded, unable to acquire permit")
                
                # 调用外部API爬取
                url_start_time = datetime.now()
                try:
                    result = await api_client.crawl_url(url, platform, options)
                    response_time = (datetime.now() - url_start_time).total_seconds()
                    
                    # 释放限流许可
                    await rate_limiter.release(request_id, True, response_time)
                    
                    # 处理成功结果
                    results["successful_urls"] += 1
                    results["results"].append({
                        "url": url,
                        "success": True,
                        "data": result,
                        "response_time": response_time
                    })
                    
                    # 保存到数据库
                    if task_id and result.get("data"):
                        await _save_product_snapshot(task_id, url, result["data"])
                    
                    logger.debug(f"Successfully crawled {url} in {response_time:.2f}s")
                    
                except Exception as e:
                    response_time = (datetime.now() - url_start_time).total_seconds()
                    await rate_limiter.release(request_id, False, response_time)
                    
                    # 处理失败结果
                    results["failed_urls"] += 1
                    error_info = {
                        "url": url,
                        "success": False,
                        "error": str(e),
                        "response_time": response_time
                    }
                    results["errors"].append(error_info)
                    
                    logger.error(f"Failed to crawl {url}: {e}")
                
                # 更新进度
                if task_id:
                    progress = int((i + 1) / len(urls) * 100)
                    await _update_task_progress(task_id, progress)
                
                # 避免过快请求
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.error(f"Error processing URL {url}: {e}")
                results["failed_urls"] += 1
                results["errors"].append({
                    "url": url,
                    "success": False,
                    "error": str(e),
                    "response_time": 0.0
                })
        
        # 完成处理
        end_time = datetime.now()
        results["end_time"] = end_time.isoformat()
        results["duration"] = (end_time - start_time).total_seconds()
        
        # 更新任务状态
        if task_id:
            final_status = "completed" if results["failed_urls"] == 0 else "partial"
            await _update_task_status(
                task_id, 
                final_status, 
                100,
                results["successful_urls"],
                results["failed_urls"]
            )
        
        logger.info(
            f"Batch {batch_id} completed: "
            f"{results['successful_urls']}/{results['total_urls']} successful "
            f"in {results['duration']:.2f}s"
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Batch {batch_id} failed: {e}")
        
        # 更新任务状态为失败
        if task_id:
            await _update_task_status(task_id, "failed", 0)
        
        results["errors"].append({
            "batch_error": str(e),
            "timestamp": datetime.now().isoformat()
        })
        
        raise
        
    finally:
        # 清理资源
        if rate_limiter:
            await rate_limiter.stop()
        if api_client:
            await api_client.close()


async def _update_task_status(
    task_id: int,
    status: str,
    progress: int,
    success_count: int = 0,
    failed_count: int = 0
) -> None:
    """更新任务状态"""
    try:
        async with get_async_session() as session:
            update_data = {
                "status": status,
                "progress": progress
            }
            
            if status == "running" and progress == 0:
                update_data["started_at"] = datetime.now()
            elif status in ["completed", "partial", "failed"]:
                update_data["completed_at"] = datetime.now()
                if success_count > 0:
                    update_data["success_count"] = success_count
                if failed_count > 0:
                    update_data["failed_count"] = failed_count
            
            await session.execute(
                update(CrawlTask)
                .where(CrawlTask.id == task_id)
                .values(**update_data)
            )
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to update task status: {e}")


async def _update_task_progress(task_id: int, progress: int) -> None:
    """更新任务进度"""
    try:
        async with get_async_session() as session:
            await session.execute(
                update(CrawlTask)
                .where(CrawlTask.id == task_id)
                .values(progress=progress)
            )
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to update task progress: {e}")


async def _save_product_snapshot(
    task_id: int,
    url: str,
    product_data: Dict[str, Any]
) -> None:
    """保存商品快照数据"""
    try:
        async with get_async_session() as session:
            snapshot = ProductSnapshot(
                task_id=task_id,
                product_url=url,
                product_name=product_data.get("title", ""),
                current_price=product_data.get("price", 0.0),
                original_price=product_data.get("original_price", 0.0),
                discount_rate=product_data.get("discount_rate", 0.0),
                stock_quantity=product_data.get("stock", 0),
                sales_count=product_data.get("sales", 0),
                rating_score=product_data.get("rating", 0.0),
                rating_count=product_data.get("rating_count", 0),
                image_url=product_data.get("image_url", ""),
                raw_data=product_data,
                snapshot_time=datetime.now()
            )
            
            session.add(snapshot)
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to save product snapshot: {e}")


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 30}
)
def crawl_single_url(
    self,
    url: str,
    platform: str = "mercadolibre",
    task_id: Optional[int] = None,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """单URL爬取任务
    
    Args:
        url: 目标URL
        platform: 平台类型
        task_id: 数据库任务ID
        options: 爬取选项
        
    Returns:
        Dict[str, Any]: 爬取结果
    """
    return asyncio.run(_crawl_single_url_async(url, platform, task_id, options))


async def _crawl_single_url_async(
    url: str,
    platform: str,
    task_id: Optional[int],
    options: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """异步单URL爬取实现"""
    
    start_time = datetime.now()
    rate_limiter = RateLimiter(RateLimitConfig())

    # 检查是否有指定的爬虫后端
    crawler_backend = options.get("crawler_backend")
    if crawler_backend:
        # 使用指定的后端配置
        api_config = APIConfig(
            base_url=crawler_backend.get("base_url", "http://localhost:11234"),
            timeout=crawler_backend.get("timeout", 30.0),
            max_retries=crawler_backend.get("max_retries", 3),
            api_key=crawler_backend.get("api_key"),
            auth_type=crawler_backend.get("auth_type", "none")
        )
        logger.info(f"Using crawler backend for single URL: {crawler_backend.get('name')} ({crawler_backend.get('base_url')})")
    else:
        # 使用默认配置
        api_config = APIConfig.from_env()
        logger.info("Using default crawler backend configuration for single URL")

    api_client = CrawlerAPIClient(api_config)
    
    try:
        await rate_limiter.start()
        await api_client.start()
        
        # 获取限流许可
        request_id = f"single_{int(start_time.timestamp())}"
        if not await rate_limiter.acquire(request_id):
            raise Exception("Rate limit exceeded")
        
        # 调用外部API
        try:
            result = await api_client.crawl_url(url, platform, options)
            response_time = (datetime.now() - start_time).total_seconds()
            
            await rate_limiter.release(request_id, True, response_time)
            
            # 保存结果
            if task_id and result.get("data"):
                await _save_product_snapshot(task_id, url, result["data"])
            
            return {
                "url": url,
                "success": True,
                "data": result,
                "response_time": response_time
            }
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            await rate_limiter.release(request_id, False, response_time)
            raise
            
    finally:
        if rate_limiter:
            await rate_limiter.stop()
        if api_client:
            await api_client.close()
